import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { collection, getDocs, doc, updateDoc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

// Admin-only endpoint to sync email verification status between Firebase Auth and Firestore
export async function POST(request: NextRequest) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()
    
    // Check if user is admin
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }

    console.log('Starting email verification sync...')

    // Get all users from Firestore
    const usersSnapshot = await getDocs(collection(db, 'users'))
    const users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    console.log(`Found ${users.length} users to sync`)

    let syncedCount = 0
    let errorCount = 0
    const results = []

    // Process each user
    for (const user of users) {
      try {
        // Get Firebase Auth user record
        const authUser = await auth.getUser(user.id)
        const firebaseEmailVerified = authUser.emailVerified
        const firestoreEmailVerified = user.email_verified

        // Check if sync is needed
        if (firebaseEmailVerified !== firestoreEmailVerified) {
          // Update Firestore to match Firebase Auth
          await updateDoc(doc(db, 'users', user.id), {
            email_verified: firebaseEmailVerified,
            updated_at: new Date().toISOString(),
            email_verification_synced_at: new Date().toISOString()
          })

          syncedCount++
          results.push({
            userId: user.id,
            email: user.email,
            username: user.username,
            before: firestoreEmailVerified,
            after: firebaseEmailVerified,
            status: 'synced'
          })

          console.log(`Synced user ${user.id}: ${firestoreEmailVerified} -> ${firebaseEmailVerified}`)
        } else {
          results.push({
            userId: user.id,
            email: user.email,
            username: user.username,
            email_verified: firebaseEmailVerified,
            status: 'already_synced'
          })
        }
      } catch (error) {
        errorCount++
        console.error(`Error syncing user ${user.id}:`, error)
        results.push({
          userId: user.id,
          email: user.email,
          username: user.username,
          status: 'error',
          error: error.message
        })
      }
    }

    console.log(`Email verification sync completed: ${syncedCount} synced, ${errorCount} errors`)

    return NextResponse.json({
      message: 'Email verification sync completed',
      summary: {
        totalUsers: users.length,
        syncedUsers: syncedCount,
        alreadySynced: users.length - syncedCount - errorCount,
        errors: errorCount
      },
      results: results
    })

  } catch (error) {
    console.error('Error syncing email verification:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 })
  }
}

// GET endpoint to check sync status
export async function GET(request: NextRequest) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const userId = decodedClaims.uid

    // Get user data from Firestore to check role
    const userDoc = await getDoc(doc(db, 'users', userId))
    if (!userDoc.exists()) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userData = userDoc.data()
    
    // Check if user is admin
    if (userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }

    // Get all users and check sync status
    const usersSnapshot = await getDocs(collection(db, 'users'))
    const users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }))

    let needsSyncCount = 0
    let verifiedCount = 0
    let unverifiedCount = 0

    for (const user of users) {
      try {
        const authUser = await auth.getUser(user.id)
        const firebaseEmailVerified = authUser.emailVerified
        const firestoreEmailVerified = user.email_verified

        if (firebaseEmailVerified !== firestoreEmailVerified) {
          needsSyncCount++
        }

        if (firebaseEmailVerified) {
          verifiedCount++
        } else {
          unverifiedCount++
        }
      } catch (error) {
        console.error(`Error checking user ${user.id}:`, error)
      }
    }

    return NextResponse.json({
      totalUsers: users.length,
      verifiedUsers: verifiedCount,
      unverifiedUsers: unverifiedCount,
      needsSync: needsSyncCount,
      lastSyncCheck: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error checking sync status:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error.message 
    }, { status: 500 })
  }
}
