"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState } from "react"
import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateEmail,
  update<PERSON><PERSON>word,
  EmailAuthProvider,
  reauthenticateWithCredential,
  User as FirebaseUser,
  sendPasswordResetEmail,
  sendEmailVerification,
  applyActionCode
} from "firebase/auth"
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs, arrayUnion } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { auth, db, storage } from "@/lib/firebase"
import { activityTracker } from "@/lib/activity-tracker"
import { securityService } from "@/lib/security-service"

type User = {
  id: string
  username: string
  email: string
  name: string
  role: "admin" | "user"
  contribution_count: number
  profilePicture?: string
  isDisabled?: boolean
  email_verified: boolean
  created_at: Date
  updated_at: Date
  last_login?: {
    timestamp: Date
    ip_address: string
    user_agent: string
  }
  registration_details?: {
    ip_address: string
    user_agent: string
  }
}

type AuthContextType = {
  user: User | null
  login: (identifier: string, password: string, recaptchaToken?: string) => Promise<void>
  signup: (email: string, password: string, name: string, username: string, recaptchaToken?: string) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (data: {
    name?: string
    username?: string
    email?: string
    currentPassword?: string
    newPassword?: string
    profilePicture?: File
  }) => Promise<void>
  resetPassword: (email: string) => Promise<void>
  sendVerificationEmail: () => Promise<void>
  verifyEmail: (code: string) => Promise<void>
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        // Get additional user data from Firestore
        const userDoc = await getDoc(doc(db, "users", firebaseUser.uid))
        if (userDoc.exists()) {
          const userData = userDoc.data()

          // Always use Firebase Auth's emailVerified status as the source of truth
          const isEmailVerified = firebaseUser.emailVerified

          // If Firestore email_verified doesn't match Firebase Auth, update it
          if (userData.email_verified !== isEmailVerified) {
            try {
              await updateDoc(doc(db, "users", firebaseUser.uid), {
                email_verified: isEmailVerified,
                updated_at: new Date()
              })
              console.log(`Updated email_verified status to ${isEmailVerified} for user ${firebaseUser.uid}`)
            } catch (error) {
              console.error('Error syncing email_verified status:', error)
            }
          }

          setUser({
            id: firebaseUser.uid,
            email: firebaseUser.email!,
            username: userData.username,
            name: userData.name,
            role: userData.role,
            contribution_count: userData.contribution_count || 0,
            profilePicture: userData.profilePicture,
            isDisabled: userData.isDisabled,
            email_verified: isEmailVerified, // Use Firebase Auth status
            created_at: userData.created_at,
            updated_at: userData.updated_at,
            last_login: userData.last_login,
            registration_details: userData.registration_details
          })
        }
      } else {
        setUser(null)
      }
      setIsLoading(false)
    })

    return () => unsubscribe()
  }, [])

  const signup = async (email: string, password: string, name: string, username: string) => {
    setIsLoading(true)
    try {
      // Check if username is already taken
      const usersRef = collection(db, "users")
      const usernameQuery = query(usersRef, where("username", "==", username))
      const usernameSnapshot = await getDocs(usernameQuery)
      
      if (!usernameSnapshot.empty) {
        throw new Error("Username is already taken")
      }

      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user

      // Get IP address and user agent
      const clientInfoResponse = await fetch('/api/auth/get-client-info')
      const clientInfo = await clientInfoResponse.json()

      // Create user document in Firestore
      await setDoc(doc(db, "users", firebaseUser.uid), {
        email: email,
        name: name,
        username: username,
        role: "user",
        contribution_count: 0,
        isDisabled: false,
        email_verified: false,
        created_at: new Date(),
        updated_at: new Date(),
        registration_details: {
          ip_address: clientInfo.ip_address,
          user_agent: clientInfo.user_agent
        }
      })

      // Send verification email using our custom endpoint
      const verificationResponse = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email,
          ip_address: clientInfo.ip_address,
          user_agent: clientInfo.user_agent
        }),
      });

      if (!verificationResponse.ok) {
        throw new Error('Failed to send verification email');
      }

      // Sign out the user immediately after signup
      await signOut(auth);

      return {
        message: "Account created successfully! Please check your email to verify your account.",
        requiresVerification: true
      }
    } catch (error: any) {
      console.error("Signup failed:", error)
      throw new Error(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (identifier: string, password: string, recaptchaToken?: string) => {
    setIsLoading(true)
    try {
      // Verify reCAPTCHA if token is provided
      if (recaptchaToken && recaptchaToken !== 'dev-bypass-token') {
        try {
          const recaptchaResponse = await fetch('/api/auth/verify-recaptcha', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token: recaptchaToken }),
          })

          if (!recaptchaResponse.ok) {
            throw new Error('reCAPTCHA verification failed. Please try again.')
          }
        } catch (recaptchaError) {
          console.warn('reCAPTCHA verification failed:', recaptchaError)
          // In development, continue anyway
          if (process.env.NODE_ENV !== 'development') {
            throw recaptchaError
          }
        }
      }
      // First try to find user by username
      const usersRef = collection(db, "users")
      const q = query(usersRef, where("username", "==", identifier))
      const querySnapshot = await getDocs(q)
      
      let emailToUse = identifier
      let userData: any = null
      let userId: string | null = null
      
      if (!querySnapshot.empty) {
        // Found user by username
        const userDoc = querySnapshot.docs[0]
        userData = userDoc.data()
        userId = userDoc.id
        emailToUse = userData.email
      } else {
        // Try to find user by email
        const emailQuery = query(usersRef, where("email", "==", identifier))
        const emailSnapshot = await getDocs(emailQuery)
        if (!emailSnapshot.empty) {
          userData = emailSnapshot.docs[0].data()
          userId = emailSnapshot.docs[0].id
        }
      }

      // Check if user is disabled
      if (userData?.isDisabled) {
        setIsLoading(false)
        return Promise.reject({
          code: 'auth/account-disabled',
          message: 'This account has been disabled. Please contact an administrator.'
        })
      }

      try {
        // Check Firebase connectivity first
        if (process.env.NODE_ENV === 'development') {
          console.log('🔥 Attempting Firebase authentication...')
        }

        // Try to sign in first to get the Firebase Auth user
        const userCredential = await signInWithEmailAndPassword(auth, emailToUse, password)
        const firebaseUser = userCredential.user

        // Get IP address and user agent
        const clientInfoResponse = await fetch('/api/auth/get-client-info')
        const clientInfo = await clientInfoResponse.json()

        // Update last login information
        if (userId) {
          await updateDoc(doc(db, "users", userId), {
            last_login: {
              timestamp: new Date(),
              ip_address: clientInfo.ip_address,
              user_agent: clientInfo.user_agent
            },
            updated_at: new Date()
          });

          // Analyze login attempt for security
          const securityAnalysis = await securityService.analyzeLoginAttempt(
            userId,
            clientInfo.ip_address,
            clientInfo.user_agent,
            true
          );

          // Create login session
          const deviceInfo = securityService.parseUserAgent(clientInfo.user_agent);
          await securityService.createSession({
            userId,
            session_id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ip_address: clientInfo.ip_address,
            user_agent: clientInfo.user_agent,
            device_info: {
              ...deviceInfo,
              is_trusted: !securityAnalysis.suspicious
            }
          });

          // Track login activity
          await activityTracker.trackLogin(userId);
        }

        // Check if email is verified using Firebase Auth
        if (!firebaseUser.emailVerified) {
          // Sign out the user since they're not verified
          await signOut(auth)

          // Update Firestore to match Firebase Auth state
          if (userId) {
            await updateDoc(doc(db, "users", userId), {
              email_verified: false,
              updated_at: new Date()
            });
          }

          // Check if we've already sent a verification email recently (within last 5 minutes)
          const lastVerificationSent = userData?.lastVerificationSent;
          const now = new Date().getTime();
          const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds

          if (!lastVerificationSent || (now - lastVerificationSent > fiveMinutes)) {
            // Send a new verification email
            try {
              const verificationResponse = await fetch('/api/auth/verify-email', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                  email: emailToUse,
                  ip_address: clientInfo.ip_address,
                  user_agent: clientInfo.user_agent
                }),
              });

              if (verificationResponse.ok) {
                // Update the lastVerificationSent timestamp
                await updateDoc(doc(db, "users", userId), {
                  lastVerificationSent: now
                });
              }
            } catch (error) {
              console.error('Error sending verification email:', error);
              // Don't throw the error, just log it
            }
          }

          setIsLoading(false)
          return Promise.reject({
            code: 'auth/email-not-verified',
            message: 'Please verify your email address before logging in. ' + 
              (!lastVerificationSent || (now - lastVerificationSent > fiveMinutes) 
                ? 'A verification email has been sent to your inbox.' 
                : 'Please check your inbox for the verification link.')
          })
        }

        // If Firebase Auth says email is verified, update Firestore to match
        if (userId && (!userData?.email_verified || userData?.email_verified === false)) {
          await updateDoc(doc(db, "users", userId), {
            email_verified: true,
            updated_at: new Date()
          });
        }
        
        // Get the ID token
        const idToken = await firebaseUser.getIdToken()
        
        // Set the session cookie
        await fetch('/api/auth/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ idToken }),
        })
      } catch (error: any) {
        // Handle all authentication errors with appropriate messages
        console.error("Login failed:", error);

        // Handle network connectivity issues
        if (error.code === 'auth/network-request-failed') {
          return Promise.reject({
            code: 'auth/network-request-failed',
            message: process.env.NODE_ENV === 'development'
              ? 'Network connection failed. This is common in development. Please check your internet connection or Firebase configuration.'
              : 'Network connection failed. Please check your internet connection and try again.'
          });
        }

        // Special handling for invalid email format
        if (error.code === 'auth/invalid-email') {
          return Promise.reject({
            code: 'auth/invalid-email',
            message: 'Please enter a valid email address.'
          });
        }

        // Handle specific Firebase Auth errors
        switch (error.code) {
          case 'auth/user-not-found':
          case 'auth/wrong-password':
          case 'auth/invalid-credential':
            return Promise.reject({
              code: 'auth/invalid-credentials',
              message: 'Invalid email or password. Please check your credentials and try again.'
            });
          case 'auth/too-many-requests':
            return Promise.reject({
              code: 'auth/too-many-requests',
              message: 'Too many failed login attempts. Please try again later.'
            });
          case 'auth/user-disabled':
            return Promise.reject({
              code: 'auth/user-disabled',
              message: 'This account has been disabled. Please contact support.'
            });
          case 'auth/configuration-not-found':
            return Promise.reject({
              code: 'auth/configuration-not-found',
              message: 'Firebase configuration error. Please contact support.'
            });
          default:
            return Promise.reject({
              code: 'auth/invalid-credentials',
              message: 'Incorrect username or password. Please try again.'
            });
        }
      }
    } catch (error: any) {
      console.error("Login failed:", error);
      return Promise.reject({
        code: 'auth/unknown-error',
        message: 'An error occurred during login. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      // Track logout activity before signing out
      if (user) {
        await activityTracker.trackLogout(user.id);
      }

      await signOut(auth)

      // Clear the session cookie
      await fetch('/api/auth/session', {
        method: 'DELETE',
      })

      setUser(null)
    } catch (error) {
      console.error("Logout failed:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const sendVerificationEmail = async () => {
    if (!auth.currentUser) {
      throw new Error("No user logged in")
    }

    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: auth.currentUser.email }),
      });

      if (!response.ok) {
        throw new Error('Failed to send verification email');
      }
    } catch (error: any) {
      console.error("Error sending verification email:", error)
      throw new Error("Failed to send verification email. Please try again.")
    }
  }

  const verifyEmail = async (code: string) => {
    try {
      await applyActionCode(auth, code)
    } catch (error: any) {
      console.error("Error verifying email:", error)
      throw new Error("Failed to verify email. The verification link may have expired.")
    }
  }

  const updateProfile = async (data: { 
    name?: string
    username?: string
    email?: string
    currentPassword?: string
    newPassword?: string
    profilePicture?: File 
  }) => {
    if (!user) throw new Error("No user logged in")
    
    setIsLoading(true)
    try {
      const firebaseUser = auth.currentUser
      if (!firebaseUser) throw new Error("No user logged in")

      const updates: any = {}
      
      // Update email if provided
      if (data.email && data.email !== user.email) {
        if (!data.currentPassword) {
          throw new Error("Current password is required to update email")
        }
        try {
          // Reauthenticate user
          const credential = EmailAuthProvider.credential(user.email, data.currentPassword)
          await reauthenticateWithCredential(firebaseUser, credential)

          // Check if email is verified
          if (!firebaseUser.emailVerified) {
            // Send verification email
            await sendEmailVerification(firebaseUser)
            throw new Error("Please verify your current email first. A verification email has been sent.")
          }

          await updateEmail(firebaseUser, data.email)
          updates.email = data.email
        } catch (error: any) {
          if (error.code === 'auth/operation-not-allowed') {
            throw new Error("Email verification is required before changing email. Please verify your current email first.")
          } else if (error.code === 'auth/email-already-in-use') {
            throw new Error("This email is already in use by another account.")
          } else if (error.code === 'auth/invalid-credential') {
            throw new Error("Current password is incorrect.")
          } else {
            throw new Error(error.message || "Failed to update email. Please try again.")
          }
        }
      }

      // Update password if provided
      if (data.newPassword) {
        if (!data.currentPassword) {
          throw new Error("Current password is required to update password")
        }
        try {
          // Reauthenticate user
          const credential = EmailAuthProvider.credential(user.email, data.currentPassword)
          await reauthenticateWithCredential(firebaseUser, credential)
          await updatePassword(firebaseUser, data.newPassword)
        } catch (error: any) {
          if (error.code === 'auth/invalid-credential') {
            throw new Error("Current password is incorrect.")
          } else {
            throw new Error("Failed to update password. Please try again.")
          }
        }
      }

      // Update profile picture if provided
      if (data.profilePicture) {
        try {
          const storageRef = ref(storage, `profile-pictures/${user.id}`)
          await uploadBytes(storageRef, data.profilePicture)
          const downloadURL = await getDownloadURL(storageRef)
          updates.profilePicture = downloadURL
        } catch (error: any) {
          throw new Error("Failed to update profile picture. Please try again.")
        }
      }

      // Update Firestore document
      if (data.name) updates.name = data.name
      if (data.username) updates.username = data.username
      updates.updated_at = new Date()

      if (Object.keys(updates).length > 0) {
        await updateDoc(doc(db, "users", user.id), updates)

        // Track profile update activity
        const updatedFields = Object.keys(updates).filter(key => key !== 'updated_at')
        if (updatedFields.length > 0) {
          await activityTracker.trackProfileUpdate(user.id, updatedFields)
        }

        // Track password change separately if it occurred
        if (data.newPassword) {
          await activityTracker.trackPasswordChange(user.id)

          // Track security event for password change
          const clientInfoResponse = await fetch('/api/auth/get-client-info')
          const clientInfo = await clientInfoResponse.json()
          await securityService.trackPasswordChange(
            user.id,
            clientInfo.ip_address,
            clientInfo.user_agent
          )
        }

        // Update local user state
        setUser(prev => prev ? {
          ...prev,
          ...updates
        } : null)
      }
    } catch (error: any) {
      console.error("Profile update failed:", error)
      throw new Error(error.message || "Failed to update profile")
    } finally {
      setIsLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to send password reset email');
      }
    } catch (error: any) {
      console.error("Password reset failed:", error)
      throw new Error(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      login, 
      signup, 
      logout, 
      updateProfile, 
      resetPassword, 
      sendVerificationEmail,
      verifyEmail,
      isLoading 
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

// Export Firebase instances for use in other components
export { auth, db, storage }
