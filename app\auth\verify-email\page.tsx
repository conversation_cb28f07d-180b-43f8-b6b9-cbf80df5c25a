"use client"

import { Suspense } from "react"
import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useSearchParams, useRouter } from "next/navigation"
import { doc, updateDoc } from "firebase/firestore"
import { db } from "@/lib/firebase"

function VerifyEmailContent() {
  const { verifyEmail, user } = useAuth()
  const { toast } = useToast()
  const [isVerifying, setIsVerifying] = useState(false)
  const searchParams = useSearchParams()
  const router = useRouter()
  const code = searchParams.get("oobCode")

  useEffect(() => {
    const verifyEmailCode = async () => {
      if (!code) {
        toast({
          title: "Invalid verification link",
          description: "The verification link is invalid or has expired.",
          variant: "destructive",
        })
        router.push("/")
        return
      }

      setIsVerifying(true)
      try {
        // Verify the email with Firebase Auth
        await verifyEmail(code)

        // Update the emailVerified field in Firestore
        if (user) {
          await updateDoc(doc(db, "users", user.id), {
            emailVerified: true,
            updated_at: new Date()
          })
        }

        toast({
          title: "Email verified",
          description: "Your email has been verified successfully. You can now log in.",
        })
        router.push("/")
      } catch (error: any) {
        toast({
          title: "Verification failed",
          description: error.message || "Failed to verify email. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsVerifying(false)
      }
    }

    verifyEmailCode()
  }, [code, verifyEmail, toast, router, user])

  return (
    <div className="container flex items-center justify-center min-h-screen py-12">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Verifying Email</CardTitle>
          <CardDescription>
            Please wait while we verify your email address...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          {isVerifying ? (
            <Loader2 className="h-8 w-8 animate-spin" />
          ) : (
            <p>Redirecting to login...</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            disabled={isVerifying}
          >
            Go to Login
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyEmailContent />
    </Suspense>
  )
} 